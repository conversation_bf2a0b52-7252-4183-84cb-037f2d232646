@extends('layouts.admin')

@section('title', 'New Reservation - SMP Online')

@section('content')
    <!-- Critical inline CSS and JS to prevent white flash -->
    <style>
        .utility-section-disabled { opacity: 0.6; pointer-events: none; }
        .utility-table-disabled { opacity: 0.6; }
        .utility-section-enabled { opacity: 1; pointer-events: auto; }
        .utility-table-enabled { opacity: 1; }
    </style>
    <script>
        // Critical path: immediately hide elements that should be hidden on load
        document.addEventListener('DOMContentLoaded', function() {
            const costDisplay = document.getElementById('costDisplay');
            const costOverviewCard = document.getElementById('costOverviewCard');
            if (costDisplay) costDisplay.classList.add('d-none');
            if (costOverviewCard) costOverviewCard.classList.add('d-none');
        });
    </script>

    <!-- Page Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservation Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('reservations.index') }}">Reservations</a></li>
                    <li class="breadcrumb-item active" aria-current="page">New</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Reservation Form -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="bx bx-plus-circle me-2"></i>Create New Reservation
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('reservations.index') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-list me-1"></i>View List
                        </a>
                        <a href="{{ route('calendar.index') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-calendar me-1"></i>View Calendar
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('reservations.store') }}" id="reservationForm">
                        @csrf

                        <div class="row gy-4">
                            <!-- Left Column: Field Selection, Utilities, and Cost Overview -->
                            <div class="col-xl-6">
                                <!-- Field Selection Section -->
                                <div class="card custom-card shadow-none border mb-4">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Field Selection
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Field Selection -->
                                            <div class="col-xl-12">
                                                <label for="field_id" class="form-label">Field <span
                                                        class="text-danger">*</span></label>
                                                <select name="field_id" id="field_id" required
                                                    onchange="updateFieldInfo(); loadAvailability(); checkUtilityPrerequisites(); enableProgressiveFields();"
                                                    class="form-select @error('field_id') is-invalid @enderror">
                                                    <option value="">Select Field</option>
                                                    @foreach ($fields as $field)
                                                        <option value="{{ $field->id }}"
                                                            data-rate="{{ $field->hourly_rate }}"
                                                            data-rate2="{{ $field->night_hourly_rate }}"
                                                            data-capacity="{{ $field->capacity }}"
                                                            data-type="{{ $field->type }}"
                                                            data-opening="{{ $field->opening_time }}"
                                                            data-closing="{{ $field->closing_time }}"
                                                            data-min-hours="{{ $field->min_booking_hours }}"
                                                            data-max-hours="{{ $field->max_booking_hours }}"
                                                            data-anochi="{{ $field->night_time_start }}"
                                                            {{ old('field_id', $selectedField?->id) == $field->id ? 'selected' : '' }}>
                                                            {{ $field->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('field_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Field Information Display -->
                                            <div class="col-xl-12">
                                                <div id="fieldInfo"
                                                    class="alert alert-info {{ $selectedField ? '' : 'd-none' }}">
                                                    <p class="mb-1"><i class="ti ti-info-circle me-2"></i><strong>Field Information</strong></p>
                                                    <p class="mb-1" style="margin-left: 1.3rem;"><strong>Capacity:</strong> <span
                                                            id="fieldCapacity">{{ $selectedField?->capacity }}</span>
                                                        people</p>
                                                    <p class="mb-1" style="margin-left: 1.3rem;"><strong>Rates:</strong> Day XCG <span
                                                            id="fieldRate">{{ $selectedField ? number_format($selectedField->hourly_rate, 2) : '0.00' }}</span>/hr, Night XCG <span
                                                            id="fieldRate2">{{ $selectedField ? number_format($selectedField->night_hourly_rate, 2) : '0.00' }}</span>/hr
                                                    </p>
                                                    <p class="mb-0" style="margin-left: 1.3rem;"><strong>Work Hours:</strong> <span
                                                            id="fieldHours">{{ $selectedField?->opening_time }} -
                                                            {{ $selectedField?->closing_time }}</span> | <strong>Duration:</strong> <span
                                                            id="fieldDuration">{{ $selectedField?->min_booking_hours }} -
                                                            {{ $selectedField?->max_booking_hours }}</span> hours</p>
                                                </div>
                                            </div>

                                            <!-- Date Selection -->
                                            <div class="col-xl-12">
                                                <x-custom-date-picker
                                                    name="booking_date"
                                                    id="booking_date"
                                                    :value="old('booking_date', $selectedDate ?: date('Y-m-d'))"
                                                    label="Date"
                                                    :required="true"
                                                    :on-change="'loadAvailability(); checkUtilityPrerequisites(); enableProgressiveFields();'" />
                                            </div>

                                            <!-- Start Time Selection -->
                                            <div class="col-xl-6">
                                                <label for="start_time" class="form-label">Start Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="start_time" id="start_time" required disabled
                                                    onchange="updateEndTimeOptions();"
                                                    class="form-select @error('start_time') is-invalid @enderror">
                                                    <option value="">Select Start Time</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                <div class="form-text" id="startTimeHelp">
                                                    <i class="ti ti-info-circle me-1"></i>Please select a field first
                                                </div>
                                                @error('start_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- End Time Selection -->
                                            <div class="col-xl-6">
                                                <label for="end_time" class="form-label">End Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="end_time" id="end_time" required disabled
                                                    onchange="handleEndTimeChange();"
                                                    class="form-select @error('end_time') is-invalid @enderror">
                                                    <option value="">Select End Time</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                <div class="form-text" id="endTimeHelp">
                                                    <i class="ti ti-info-circle me-1"></i>Please select a start time first
                                                </div>
                                                @error('end_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Utility Selection Section -->
                                <div class="card custom-card shadow-none border mb-4">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Utility Selection (Optional)
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Utilities -->
                                            <div class="col-xl-12">
                                                <label class="form-label">Add Utility</label>

                                                <!-- Utility Selection Section -->
                                                <div id="utilitySelectionSection" class="utility-section-disabled">
                                                    <div class="row g-2 align-items-center mb-3">
                                                        <div class="col-md-5">
                                                            <select id="utilitySelect" class="form-select" disabled>
                                                                <option value="">Select Utility</option>
                                                                @foreach ($utilities as $utility)
                                                                    <option value="{{ $utility->id }}"
                                                                        data-name="{{ $utility->name }}"
                                                                        data-rate="{{ $utility->hourly_rate }}">
                                                                        {{ $utility->name }} -
                                                                        XCG {{ number_format($utility->hourly_rate, 2) }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <input type="number" id="utilityQuantity"
                                                                class="form-control" min="1" step="1"
                                                                value="1" placeholder="Quantity" disabled>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <button type="button" class="btn btn-primary w-100"
                                                                onclick="addUtility()" id="addUtilityBtn"
                                                                disabled>Add</button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div id="utilityTableContainer" class="utility-table-enabled">
                                                    <div class="table-responsive">
                                                        <table class="table table-bordered text-nowrap" id="utilityTable">
                                                            <thead id="utilityTableHeader" class="d-none">
                                                                <tr>
                                                                    <th>Utility</th>
                                                                    <th>Quantity</th>
                                                                    <th>Rate</th>
                                                                    <th>Cost</th>
                                                                    <th>Action</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="utilityTableBody">
                                                                <!-- Dynamic rows will be added here -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Utility Prerequisites Message -->
                                                <div id="utilityPrerequisitesMessage" class="form-text mb-1">
                                                    <p class="mb-0"><i class="ti ti-info-circle me-2"></i><strong>Complete reservation details first</strong></p>
                                                    <p class="mb-0" style="margin-left: 1.3rem;">Please select field, date, start time, and end time before adding utilities.</p>
                                                </div>
                                            </div>
                                            <!-- end Utilities -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Total Cost Overview Section -->
                                <div class="card custom-card shadow-none border mb-4 d-none" id="costOverviewCard">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Total Cost Overview
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Availability Check -->
                                            <div class="col-xl-12">
                                                <div id="availabilityCheck" class="d-none">
                                                    <div id="availabilityMessage" class="alert"></div>
                                                </div>
                                            </div>

                                            <!-- Enhanced Cost Display with Detailed Breakdown -->
                                            <div class="col-xl-12">
                                                <div id="costDisplay" class="alert alert-success d-none">
                                                    <h6 class="fw-semibold">Reservation Cost Breakdown</h6>

                                                    <!-- Field Cost Breakdown -->
                                                    <div id="fieldCostBreakdown" class="mb-2">
                                                        <div class="fw-semibold mb-1">Field Cost:</div>
                                                        <div id="dayNightBreakdown" class="fs-12 text-muted mb-1">
                                                            <!-- Day/Night breakdown will be populated by JavaScript -->
                                                        </div>
                                                        <div class="fs-12">
                                                            <strong>Field Total: XCG <span
                                                                    id="fieldCost">0.00</span></strong>
                                                        </div>
                                                    </div>

                                                    <!-- Utility Cost Breakdown -->
                                                    <div id="utilityCostBreakdown" class="mb-2 d-none">
                                                        <div class="fw-semibold mb-1">Utility Costs:</div>
                                                        <div id="utilityDetails" class="fs-12 text-muted mb-1">
                                                            <!-- Utility breakdown will be populated by JavaScript -->
                                                        </div>
                                                        <div class="fs-12">
                                                            <strong>Utility Total: XCG <span
                                                                    id="utilityCost">0.00</span></strong>
                                                        </div>
                                                    </div>

                                                    <!-- Total Cost -->
                                                    <div class="border-top pt-2">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span class="fw-bold">Total Cost:</span>
                                                            <span class="h5 mb-0 text-success">XCG <span
                                                                    id="totalCost">0.00</span></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column: Customer Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Customer Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Customer Name -->
                                            <div class="col-xl-12">
                                                <label for="customer_name" class="form-label">Customer Name <span
                                                        class="text-danger">*</span></label>
                                                <input type="text" name="customer_name" id="customer_name" required
                                                    value="{{ old('customer_name') }}"
                                                    placeholder="Name (required)"
                                                    class="form-control @error('customer_name') is-invalid @enderror">
                                                @error('customer_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Email -->
                                            <div class="col-xl-12">
                                                <label for="customer_email" class="form-label">Customer Email</label>
                                                <input type="email" name="customer_email" id="customer_email"
                                                    value="{{ old('customer_email') }}"
                                                    placeholder="Email (optional)"
                                                    class="form-control @error('customer_email') is-invalid @enderror">
                                                @error('customer_email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Phone -->
                                            <div class="col-xl-12">
                                                <label for="customer_phone" class="form-label">Customer Phone <span
                                                        class="text-danger">*</span></label>
                                                <input type="tel" name="customer_phone" id="customer_phone" required
                                                    value="{{ old('customer_phone') }}"
                                                    placeholder="Phone number (required)"
                                                    class="form-control @error('customer_phone') is-invalid @enderror">
                                                @error('customer_phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Special Requests -->
                                            <div class="col-xl-12">
                                                <label for="special_requests" class="form-label">Special Requests</label>
                                                <textarea name="special_requests" id="special_requests" rows="3"
                                                    class="form-control @error('special_requests') is-invalid @enderror"
                                                    placeholder="Any special requirements or notes...">{{ old('special_requests') }}</textarea>
                                                @error('special_requests')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Reservation Rules -->
                                            <div class="col-xl-12">
                                                <div class="alert alert-default">
                                                    <h6 class="fw-semibold">FPMP Reservation Rules</h6>
                                                    <ul class="mb-0 fs-12">
                                                        <li> Reservations are available from 7:00 AM to 11:00 PM</li>
                                                        <li> All reservations are automatically confirmed</li>
                                                        <li> Instruct customer that Cancellations must be made at least 24 hours in advance</li>
                                                        <li> Instruct customer that Modifications must be made at least 24 hours in advance</li>
                                                        <li> Instruct customer to please arrive 15 minutes before your scheduled time</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="d-flex gap-2 justify-content-start mt-4 pt-3 border-top">
                                    <button type="submit" id="submitBtn" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>Create Reservation
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

<!-- Info Modals for Utility Validation -->
<x-confirmation-modal
    modal-id="utilityValidationModal"
    type="info"
    modal-title="Validation Error"
    title="Please select a utility and enter a valid whole number quantity."
    warning-text=""
    dismiss-text="OK"
    form-action="#"
/>

<x-confirmation-modal
    modal-id="utilityDuplicateModal"
    type="info"
    modal-title="Duplicate Utility"
    title="This utility is already added."
    warning-text=""
    dismiss-text="OK"
    form-action="#"
/>
@endsection

@push('scripts')
    <script>
        // Cache DOM elements for performance
        const elements = {
            fieldSelect: null,
            fieldInfo: null,
            fieldCapacity: null,
            fieldRate: null,
            fieldRate2: null,
            fieldHours: null,
            fieldDuration: null,
            startTimeSelect: null,
            endTimeSelect: null,
            costDisplay: null,
            costOverviewCard: null,
            startTimeHelp: null,
            endTimeHelp: null,
            utilitySelect: null,
            utilityQuantity: null,
            addUtilityBtn: null,
            utilitySection: null,
            utilityTableContainer: null,
            prerequisitesMessage: null,
            totalCost: null,
            fieldCost: null,
            dayNightBreakdown: null,
            utilityCostBreakdown: null,
            utilityDetails: null,
            utilityCostSpan: null
        };

        // Initialize DOM cache
        function initElements() {
            elements.fieldSelect = document.getElementById('field_id');
            elements.fieldInfo = document.getElementById('fieldInfo');
            elements.fieldCapacity = document.getElementById('fieldCapacity');
            elements.fieldRate = document.getElementById('fieldRate');
            elements.fieldRate2 = document.getElementById('fieldRate2');
            elements.fieldHours = document.getElementById('fieldHours');
            elements.fieldDuration = document.getElementById('fieldDuration');
            elements.startTimeSelect = document.getElementById('start_time');
            elements.endTimeSelect = document.getElementById('end_time');
            elements.costDisplay = document.getElementById('costDisplay');
            elements.costOverviewCard = document.getElementById('costOverviewCard');
            elements.startTimeHelp = document.getElementById('startTimeHelp');
            elements.endTimeHelp = document.getElementById('endTimeHelp');
            elements.utilitySelect = document.getElementById('utilitySelect');
            elements.utilityQuantity = document.getElementById('utilityQuantity');
            elements.addUtilityBtn = document.getElementById('addUtilityBtn');
            elements.utilitySection = document.getElementById('utilitySelectionSection');
            elements.utilityTableContainer = document.getElementById('utilityTableContainer');
            elements.prerequisitesMessage = document.getElementById('utilityPrerequisitesMessage');
            elements.totalCost = document.getElementById('totalCost');
            elements.fieldCost = document.getElementById('fieldCost');
            elements.dayNightBreakdown = document.getElementById('dayNightBreakdown');
            elements.utilityCostBreakdown = document.getElementById('utilityCostBreakdown');
            elements.utilityDetails = document.getElementById('utilityDetails');
            elements.utilityCostSpan = document.getElementById('utilityCost');
        }

        function updateFieldInfo() {
            const selectedOption = elements.fieldSelect.options[elements.fieldSelect.selectedIndex];

            if (selectedOption.value) {
                elements.fieldCapacity.textContent = selectedOption.dataset.capacity || 'N/A';
                elements.fieldRate.textContent = parseFloat(selectedOption.dataset.rate || 0).toFixed(2);
                elements.fieldRate2.textContent = parseFloat(selectedOption.dataset.rate2 || 0).toFixed(2);
                elements.fieldHours.textContent = (selectedOption.dataset.opening || '08:00') + ' - ' + (selectedOption.dataset.closing || '22:00');
                elements.fieldDuration.textContent = 'Min ' + (selectedOption.dataset.minHours || '1') + ' - Max ' + (selectedOption.dataset.maxHours || '8');
                elements.fieldInfo.classList.remove('d-none');
                calculateCost();
            } else {
                elements.fieldInfo.classList.add('d-none');
                elements.costDisplay.classList.add('d-none');
                elements.costOverviewCard.classList.add('d-none');
            }
            checkUtilityPrerequisites();
            enableProgressiveFields();
        }



        // Simplified end time change handler
        function handleEndTimeChange() {
            if (!elements.endTimeSelect.value) {
                if (costCalculationTimeout) {
                    clearTimeout(costCalculationTimeout);
                    costCalculationTimeout = null;
                }
                if (currentRequest) {
                    currentRequest.abort();
                    currentRequest = null;
                }
                elements.costDisplay.classList.add('d-none');
                elements.costOverviewCard.classList.add('d-none');
            }
            calculateCost();
            checkUtilityPrerequisites();
            enableProgressiveFields();
        }

        // Optimized request handling
        let costCalculationTimeout;
        let lastRequestData = null;
        let currentRequest = null;

        function calculateCost() {
            const selectedOption = elements.fieldSelect.options[elements.fieldSelect.selectedIndex];
            const rate = parseFloat(selectedOption.dataset.rate || 0);
            const fieldId = elements.fieldSelect.value;
            const startTime = elements.startTimeSelect.value;
            const endTime = elements.endTimeSelect.value;

            if (!fieldId || !startTime || !endTime) {
                elements.costDisplay.classList.add('d-none');
                elements.costOverviewCard.classList.add('d-none');
                return;
            }

            const duration = calculateDurationFromTimes(startTime, endTime);
            if (duration <= 0) {
                elements.costDisplay.classList.add('d-none');
                elements.costOverviewCard.classList.add('d-none');
                return;
            }

            const utilitiesData = utilities.map(u => ({ id: u.id, hours: u.quantity }));
            const requestData = { field_id: fieldId, start_time: startTime, end_time: endTime, utilities: utilitiesData };
            const requestDataString = JSON.stringify(requestData);

            if (lastRequestData === requestDataString) return;

            if (costCalculationTimeout) clearTimeout(costCalculationTimeout);

            costCalculationTimeout = setTimeout(() => {
                if (currentRequest) currentRequest.abort();
                lastRequestData = requestDataString;
                const controller = new AbortController();
                currentRequest = controller;

                fetch(`{{ route('reservations.cost-estimate') }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(requestData),
                    signal: controller.signal
                })
                .then(response => response.json())
                .then(data => {
                    currentRequest = null;
                    if (data.error) return;

                    const totalCost = parseFloat(data.total_cost || 0);
                    elements.totalCost.textContent = totalCost.toFixed(2);

                    const fieldCost = parseFloat(data.field_cost || data.subtotal || data.total_cost || 0);
                    elements.fieldCost.textContent = fieldCost.toFixed(2);

                    if (data.rate_breakdown && (data.rate_breakdown.day_hours > 0 || data.rate_breakdown.night_hours > 0)) {
                        let breakdownHtml = '';
                        if (data.rate_breakdown.day_hours > 0) {
                            const dayRate = parseFloat(data.hourly_rate || 0);
                            const dayCost = parseFloat(data.rate_breakdown.day_cost || 0);
                            breakdownHtml += `Day Rate: ${data.rate_breakdown.day_hours} hours × XCG ${dayRate.toFixed(2)} = XCG ${dayCost.toFixed(2)}<br>`;
                        }
                        if (data.rate_breakdown.night_hours > 0) {
                            const nightRate = parseFloat(data.night_hourly_rate || 0);
                            const nightCost = parseFloat(data.rate_breakdown.night_cost || 0);
                            breakdownHtml += `Night Rate: ${data.rate_breakdown.night_hours} hours × XCG ${nightRate.toFixed(2)} = XCG ${nightCost.toFixed(2)}`;
                        }
                        elements.dayNightBreakdown.innerHTML = breakdownHtml;
                    } else {
                        elements.dayNightBreakdown.innerHTML = `${duration} hours × XCG ${rate.toFixed(2)} = XCG ${fieldCost.toFixed(2)}`;
                    }

                    if (data.utility_breakdown && data.utility_breakdown.length > 0) {
                        let utilityHtml = '';
                        let totalUtilityCost = 0;

                        data.utility_breakdown.forEach(utility => {
                            const utilityRate = parseFloat(utility.rate || 0);
                            const utilityCost = parseFloat(utility.cost || 0);
                            utilityHtml += `${utility.name}: ${utility.hours} × XCG ${utilityRate.toFixed(2)} = XCG ${utilityCost.toFixed(2)}<br>`;
                            totalUtilityCost += utilityCost;

                            // Simplified utility cost update
                            const costCell = document.getElementById(`utility-cost-${utility.utility_id}`) ||
                                           document.querySelector(`#utilityTable tbody tr[data-utility-id="${utility.utility_id}"] td:nth-child(4)`);
                            if (costCell) {
                                costCell.innerHTML = `XCG ${utilityCost.toFixed(2)}`;
                            }
                        });

                        elements.utilityDetails.innerHTML = utilityHtml;
                        elements.utilityCostSpan.textContent = totalUtilityCost.toFixed(2);
                        elements.utilityCostBreakdown.classList.remove('d-none');
                    } else {
                        elements.utilityCostBreakdown.classList.add('d-none');
                        elements.utilityCostSpan.textContent = '0.00';
                    }

                    if (elements.startTimeSelect.value && elements.endTimeSelect.value) {
                        elements.costDisplay.className = 'alert alert-success d-block';
                        elements.costDisplay.classList.remove('d-none');
                        elements.costOverviewCard.classList.remove('d-none');
                    }
                })
                .catch(error => {
                    currentRequest = null;
                    if (error.name === 'AbortError') return;

                    elements.totalCost.textContent = 'Error';
                    elements.fieldCost.textContent = 'Error';
                    elements.costDisplay.className = 'alert alert-warning d-block';
                    elements.costDisplay.innerHTML = `
                        <h6 class="fw-semibold">Cost Calculation Unavailable</h6>
                        <p class="mb-0">Unable to calculate cost at this time. Please try again or contact support.</p>
                    `;
                    elements.costDisplay.classList.remove('d-none');
                    elements.costOverviewCard.classList.remove('d-none');
                });
            }, 150);
        }

        function loadAvailability() {
            const fieldId = elements.fieldSelect.value;
            const date = document.getElementById('booking_date').value;

            if (!fieldId || !date) return;

            const selectedOption = elements.fieldSelect.options[elements.fieldSelect.selectedIndex];
            const minDuration = parseFloat(selectedOption.dataset.minHours || 0.5);

            fetch(`{{ route('reservations.check-availability') }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    field_id: fieldId,
                    date: date,
                    duration_hours: minDuration
                })
            })
            .then(response => response.json())
            .then(data => updateTimeSlots(data.slots || []))
            .catch(() => {}); // Silent fail for availability
        }

        function updateTimeSlots(slots) {
            const currentStartValue = elements.startTimeSelect.value || '{{ $selectedTime ?? '' }}';

            elements.startTimeSelect.innerHTML = '<option value="">Select Start Time</option>';

            slots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot.start_time;
                option.textContent = slot.start_time;
                if (slot.start_time === currentStartValue) {
                    option.selected = true;
                }
                elements.startTimeSelect.appendChild(option);
            });

            const actualStartValue = elements.startTimeSelect.value;

            if (actualStartValue) {
                updateEndTimeOptions();
            } else {
                elements.endTimeSelect.disabled = true;
                elements.endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                elements.endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';

                if (costCalculationTimeout) {
                    clearTimeout(costCalculationTimeout);
                    costCalculationTimeout = null;
                }
                if (currentRequest) {
                    currentRequest.abort();
                    currentRequest = null;
                }

                elements.costDisplay.classList.add('d-none');
                elements.costOverviewCard.classList.add('d-none');

                checkUtilityPrerequisites();
                calculateCost();
                enableProgressiveFields();
            }
        }

        function checkUtilityPrerequisites() {
            const fieldId = elements.fieldSelect.value;
            const bookingDate = document.getElementById('booking_date').value;
            const startTime = elements.startTimeSelect.value;
            const endTime = elements.endTimeSelect.value;

            const allRequiredFieldsSelected = fieldId && bookingDate && startTime && endTime;

            if (allRequiredFieldsSelected) {
                elements.utilitySelect.disabled = false;
                elements.utilityQuantity.disabled = false;
                elements.addUtilityBtn.disabled = false;
                elements.utilitySection.className = 'utility-section-enabled';
                elements.utilityTableContainer.className = 'utility-table-enabled';
                elements.prerequisitesMessage.classList.add('d-none');
                elements.utilityTableContainer.style.display = '';
                enableUtilityTableInteractions(true);
            } else {
                elements.utilitySelect.disabled = true;
                elements.utilityQuantity.disabled = true;
                elements.addUtilityBtn.disabled = true;
                elements.utilitySection.className = 'utility-section-disabled';
                elements.prerequisitesMessage.classList.remove('d-none');

                if (utilities.length === 0) {
                    elements.utilityTableContainer.style.display = 'none';
                    elements.utilityTableContainer.className = 'utility-table-disabled';
                } else {
                    elements.utilityTableContainer.style.display = '';
                    elements.utilityTableContainer.className = 'utility-table-disabled';
                }

                enableUtilityTableInteractions(false);
                updatePrerequisitesMessage(fieldId, bookingDate, startTime, endTime);
            }
        }

        function updatePrerequisitesMessage(fieldId, bookingDate, startTime, endTime) {
            const missingFields = [];
            if (!fieldId) missingFields.push('field');
            if (!bookingDate) missingFields.push('date');
            if (!startTime) missingFields.push('start time');
            if (!endTime) missingFields.push('end time');

            if (missingFields.length > 0) {
                const fieldList = missingFields.join(', ');
                elements.prerequisitesMessage.innerHTML = `
                    <p class="mb-0"><i class="ti ti-info-circle me-2"></i><strong>Complete reservation details first</strong></p>
                    <p class="mb-0" style="margin-left: 1.3rem;">Please select ${fieldList} before adding utilities.</p>
                `;
            }
        }

        function enableUtilityTableInteractions(enabled) {
            document.querySelectorAll('#utilityTable .btn-danger').forEach(button => {
                button.disabled = !enabled;
                button.classList.toggle('disabled', !enabled);
                button.style.pointerEvents = enabled ? 'auto' : 'none';
            });

            document.querySelectorAll('#utilityTable input[type="number"]').forEach(input => {
                input.disabled = !enabled;
            });
        }

        function enableProgressiveFields() {
            const fieldSelected = elements.fieldSelect.value;
            const startTimeSelected = elements.startTimeSelect.value;

            if (fieldSelected) {
                elements.startTimeSelect.disabled = false;
                elements.startTimeHelp.innerHTML = '';
            } else {
                elements.startTimeSelect.disabled = true;
                elements.startTimeSelect.innerHTML = '<option value="">Select Start Time</option>';
                elements.startTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a field first';
                elements.endTimeSelect.disabled = true;
                elements.endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                elements.endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';
                return;
            }

            if (startTimeSelected) {
                elements.endTimeSelect.disabled = false;
                elements.endTimeHelp.innerHTML = '';
            } else {
                elements.endTimeSelect.disabled = true;
                elements.endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                elements.endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';

                if (costCalculationTimeout) {
                    clearTimeout(costCalculationTimeout);
                    costCalculationTimeout = null;
                }
                if (currentRequest) {
                    currentRequest.abort();
                    currentRequest = null;
                }
                elements.costDisplay.classList.add('d-none');
                elements.costOverviewCard.classList.add('d-none');
            }
        }

        function updateEndTimeOptions() {
            const startTime = elements.startTimeSelect.value;
            const selectedField = elements.fieldSelect.options[elements.fieldSelect.selectedIndex];
            const date = document.getElementById('booking_date').value;

            if (!startTime || !selectedField.value || !date) {
                elements.endTimeSelect.disabled = true;
                elements.endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                elements.endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';
                calculateCost();
                checkUtilityPrerequisites();
                enableProgressiveFields();
                return;
            }

            const currentEndTime = elements.endTimeSelect.value;

            elements.endTimeSelect.disabled = true;
            elements.endTimeSelect.innerHTML = '<option value="">Loading available times...</option>';
            elements.endTimeHelp.innerHTML = '<i class="ti ti-loader me-1"></i>Checking availability...';

            fetch(`{{ route('reservations.available-end-times') }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    field_id: selectedField.value,
                    date: date,
                    start_time: startTime
                })
            })
            .then(response => response.json())
            .then(data => {
                elements.endTimeSelect.disabled = false;
                elements.endTimeHelp.innerHTML = '';

                if (data.success && data.end_times.length > 0) {
                    elements.endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                    data.end_times.forEach(option => {
                        const optionElement = document.createElement('option');
                        optionElement.value = option.value;
                        optionElement.textContent = option.text;
                        elements.endTimeSelect.appendChild(optionElement);
                    });

                    if (currentEndTime && elements.endTimeSelect.querySelector(`option[value="${currentEndTime}"]`)) {
                        elements.endTimeSelect.value = currentEndTime;
                    }
                } else {
                    elements.endTimeSelect.innerHTML = '<option value="">No available end times</option>';
                    elements.endTimeHelp.innerHTML = '<i class="ti ti-alert-circle me-1 text-warning"></i>No available end times for this start time';
                }

                calculateCost();
                checkUtilityPrerequisites();
                enableProgressiveFields();
            })
            .catch(() => {
                elements.endTimeSelect.disabled = false;
                elements.endTimeSelect.innerHTML = '<option value="">Error loading times</option>';
                elements.endTimeHelp.innerHTML = '<i class="ti ti-alert-circle me-1 text-danger"></i>Error loading available times';
                calculateCost();
                checkUtilityPrerequisites();
                enableProgressiveFields();
            });
        }



        function calculateDurationFromTimes(startTime, endTime) {
            if (!startTime || !endTime) return 0;
            const startDate = new Date(`2000-01-01 ${startTime}`);
            const endDate = new Date(`2000-01-01 ${endTime}`);
            if (endDate <= startDate) return 0;
            return (endDate - startDate) / (1000 * 60 * 60);
        }

        function setTimeFromCalendar(startTime, defaultDuration = 1) {
            if (elements.startTimeSelect.querySelector(`option[value="${startTime}"]`)) {
                elements.startTimeSelect.value = startTime;
                updateEndTimeOptions();

                const startDate = new Date(`2000-01-01 ${startTime}`);
                const endDate = new Date(startDate.getTime() + (defaultDuration * 60 * 60 * 1000));
                const endTime = endDate.toTimeString().substring(0, 5);

                if (elements.endTimeSelect.querySelector(`option[value="${endTime}"]`)) {
                    elements.endTimeSelect.value = endTime;
                    calculateCost();
                    checkUtilityPrerequisites();
                }
            }
        }

        // Optimized initialization
        document.addEventListener('DOMContentLoaded', function() {
            initElements();
            updateFieldInfo();
            enableProgressiveFields();
            loadAvailability();
            checkUtilityPrerequisites();

            const preSelectedTime = '{{ $selectedTime ?? '' }}';
            if (preSelectedTime) {
                setTimeout(() => setTimeFromCalendar(preSelectedTime, 1), 100);
            }
        });
    </script>

    <script>
        let utilities = [];

        function getReservationState() {
            return {
                fieldId: elements.fieldSelect?.value || '',
                bookingDate: document.getElementById('booking_date')?.value || '',
                startTime: elements.startTimeSelect?.value || '',
                endTime: elements.endTimeSelect?.value || ''
            };
        }

        function restoreReservationState(state) {
            if (elements.fieldSelect && state.fieldId && elements.fieldSelect.value !== state.fieldId) {
                elements.fieldSelect.value = state.fieldId;
            }
            const dateEl = document.getElementById('booking_date');
            if (dateEl && state.bookingDate && dateEl.value !== state.bookingDate) {
                dateEl.value = state.bookingDate;
            }
            if (elements.startTimeSelect && state.startTime && elements.startTimeSelect.value !== state.startTime) {
                if (elements.startTimeSelect.querySelector(`option[value="${state.startTime}"]`)) {
                    elements.startTimeSelect.value = state.startTime;
                }
            }
            if (elements.endTimeSelect && state.endTime && elements.endTimeSelect.value !== state.endTime) {
                if (elements.endTimeSelect.querySelector(`option[value="${state.endTime}"]`)) {
                    elements.endTimeSelect.value = state.endTime;
                }
            }
        }


        function addUtility() {
            const prevState = getReservationState();

            const id = elements.utilitySelect.value;
            const name = elements.utilitySelect.options[elements.utilitySelect.selectedIndex].dataset.name;
            const rate = parseFloat(elements.utilitySelect.options[elements.utilitySelect.selectedIndex].dataset.rate || 0);
            const quantity = parseInt(elements.utilityQuantity.value || 1);

            if (!id || quantity <= 0 || !Number.isInteger(quantity)) {
                showInfoModal('utilityValidationModal');
                return;
            }

            if (utilities.find(u => u.id === id)) {
                showInfoModal('utilityDuplicateModal');
                return;
            }

            utilities.push({ id, name, rate, quantity });

            const tableBody = document.querySelector('#utilityTable tbody');
            const tableHeader = document.getElementById('utilityTableHeader');
            const row = document.createElement('tr');
            row.setAttribute('data-utility-id', id);
            row.id = `utility-row-${id}`;
            row.innerHTML = `
                <td>${name}</td>
                <td>${quantity}</td>
                <td>XCG ${rate.toFixed(2)}</td>
                <td id="utility-cost-${id}"><span class="text-muted">Calculating...</span></td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeUtility('${id}', this)">Remove</button>
                </td>
                <input type="hidden" name="utilities[${id}][id]" value="${id}">
                <input type="hidden" name="utilities[${id}][hours]" value="${quantity}">
            `;

            tableBody.appendChild(row);

            if (utilities.length === 1) {
                tableHeader.classList.remove('d-none');
            }

            elements.utilitySelect.selectedIndex = 0;
            elements.utilityQuantity.value = 1;

            restoreReservationState(prevState);
            calculateCost();
        }

        function removeUtility(id, btn) {
            const prevState = getReservationState();

            utilities = utilities.filter(u => u.id !== id);
            btn.closest('tr').remove();

            const tableHeader = document.getElementById('utilityTableHeader');
            if (utilities.length === 0) {
                tableHeader.classList.add('d-none');
            }

            restoreReservationState(prevState);
            checkUtilityPrerequisites();
            calculateCost();
        }

        function showInfoModal(modalId) {
            const modal = new bootstrap.Modal(document.getElementById(modalId));
            modal.show();
        }
    </script>
@endpush
